[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-stdlib=libc++", "-std=gnu++1z", "-is<PERSON><PERSON>", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-mmacosx-version-min=12", "-Wall", "-Wextra", "-fsyntax-only", "--target=arm64-apple-darwin24.5.0", "-DQT_NO_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Qt Creator.app/Contents/Resources/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Qt Creator.app/Contents/Resources/cplusplus/wrappedQtHeaders/QtCore", "-I/Users/<USER>/Desktop/projects/sensuser/repo/sensuser", "-I/usr/local/include/Eigen", "-I/opt/Qt/6.9.0/macos/lib/QtCharts.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtOpenGLWidgets.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtWidgets.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtOpenGL.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtGui.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtCore.framework/Headers", "-I/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework/Headers", "-I/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/AGL.framework/Headers", "-I/opt/Qt/6.9.0/macos/mkspecs/macx-clang", "-F", "/opt/Qt/6.9.0/macos/lib", "-F", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks", "-isystem", "/usr/local/include/c++/v1", "-isystem", "/opt/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-isystem", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Desktop/projects/sensuser/repo/sensuser/main.cpp"], "directory": "/Users/<USER>/Desktop/projects/sensuser/repo/sensuser/.qtc_clangd", "file": "/Users/<USER>/Desktop/projects/sensuser/repo/sensuser/main.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-stdlib=libc++", "-std=gnu++1z", "-is<PERSON><PERSON>", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-mmacosx-version-min=12", "-Wall", "-Wextra", "-fsyntax-only", "--target=arm64-apple-darwin24.5.0", "-DQT_NO_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Qt Creator.app/Contents/Resources/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Qt Creator.app/Contents/Resources/cplusplus/wrappedQtHeaders/QtCore", "-I/Users/<USER>/Desktop/projects/sensuser/repo/sensuser", "-I/usr/local/include/Eigen", "-I/opt/Qt/6.9.0/macos/lib/QtCharts.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtOpenGLWidgets.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtWidgets.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtOpenGL.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtGui.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtCore.framework/Headers", "-I/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework/Headers", "-I/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/AGL.framework/Headers", "-I/opt/Qt/6.9.0/macos/mkspecs/macx-clang", "-F", "/opt/Qt/6.9.0/macos/lib", "-F", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks", "-isystem", "/usr/local/include/c++/v1", "-isystem", "/opt/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-isystem", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Desktop/projects/sensuser/repo/sensuser/mainwindow.cpp"], "directory": "/Users/<USER>/Desktop/projects/sensuser/repo/sensuser/.qtc_clangd", "file": "/Users/<USER>/Desktop/projects/sensuser/repo/sensuser/mainwindow.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-stdlib=libc++", "-std=gnu++1z", "-is<PERSON><PERSON>", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-mmacosx-version-min=12", "-Wall", "-Wextra", "-fsyntax-only", "--target=arm64-apple-darwin24.5.0", "-DQT_NO_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Qt Creator.app/Contents/Resources/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Qt Creator.app/Contents/Resources/cplusplus/wrappedQtHeaders/QtCore", "-I/Users/<USER>/Desktop/projects/sensuser/repo/sensuser", "-I/usr/local/include/Eigen", "-I/opt/Qt/6.9.0/macos/lib/QtCharts.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtOpenGLWidgets.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtWidgets.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtOpenGL.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtGui.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtCore.framework/Headers", "-I/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework/Headers", "-I/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/AGL.framework/Headers", "-I/opt/Qt/6.9.0/macos/mkspecs/macx-clang", "-F", "/opt/Qt/6.9.0/macos/lib", "-F", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks", "-isystem", "/usr/local/include/c++/v1", "-isystem", "/opt/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-isystem", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Desktop/projects/sensuser/repo/sensuser/mlp.cpp"], "directory": "/Users/<USER>/Desktop/projects/sensuser/repo/sensuser/.qtc_clangd", "file": "/Users/<USER>/Desktop/projects/sensuser/repo/sensuser/mlp.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-stdlib=libc++", "-std=gnu++1z", "-is<PERSON><PERSON>", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-mmacosx-version-min=12", "-Wall", "-Wextra", "-fsyntax-only", "--target=arm64-apple-darwin24.5.0", "-DQT_NO_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Qt Creator.app/Contents/Resources/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Qt Creator.app/Contents/Resources/cplusplus/wrappedQtHeaders/QtCore", "-I/Users/<USER>/Desktop/projects/sensuser/repo/sensuser", "-I/usr/local/include/Eigen", "-I/opt/Qt/6.9.0/macos/lib/QtCharts.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtOpenGLWidgets.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtWidgets.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtOpenGL.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtGui.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtCore.framework/Headers", "-I/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework/Headers", "-I/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/AGL.framework/Headers", "-I/opt/Qt/6.9.0/macos/mkspecs/macx-clang", "-F", "/opt/Qt/6.9.0/macos/lib", "-F", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks", "-isystem", "/usr/local/include/c++/v1", "-isystem", "/opt/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-isystem", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Desktop/projects/sensuser/repo/sensuser/layer.cpp"], "directory": "/Users/<USER>/Desktop/projects/sensuser/repo/sensuser/.qtc_clangd", "file": "/Users/<USER>/Desktop/projects/sensuser/repo/sensuser/layer.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-stdlib=libc++", "-std=gnu++1z", "-is<PERSON><PERSON>", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-mmacosx-version-min=12", "-Wall", "-Wextra", "-fsyntax-only", "--target=arm64-apple-darwin24.5.0", "-DQT_NO_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Qt Creator.app/Contents/Resources/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Qt Creator.app/Contents/Resources/cplusplus/wrappedQtHeaders/QtCore", "-I/Users/<USER>/Desktop/projects/sensuser/repo/sensuser", "-I/usr/local/include/Eigen", "-I/opt/Qt/6.9.0/macos/lib/QtCharts.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtOpenGLWidgets.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtWidgets.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtOpenGL.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtGui.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtCore.framework/Headers", "-I/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework/Headers", "-I/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/AGL.framework/Headers", "-I/opt/Qt/6.9.0/macos/mkspecs/macx-clang", "-F", "/opt/Qt/6.9.0/macos/lib", "-F", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks", "-isystem", "/usr/local/include/c++/v1", "-isystem", "/opt/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-isystem", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Desktop/projects/sensuser/repo/sensuser/trainingworker.cpp"], "directory": "/Users/<USER>/Desktop/projects/sensuser/repo/sensuser/.qtc_clangd", "file": "/Users/<USER>/Desktop/projects/sensuser/repo/sensuser/trainingworker.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-stdlib=libc++", "-std=gnu++1z", "-is<PERSON><PERSON>", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-mmacosx-version-min=12", "-Wall", "-Wextra", "-fsyntax-only", "--target=arm64-apple-darwin24.5.0", "-DQT_NO_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Qt Creator.app/Contents/Resources/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Qt Creator.app/Contents/Resources/cplusplus/wrappedQtHeaders/QtCore", "-I/Users/<USER>/Desktop/projects/sensuser/repo/sensuser", "-I/usr/local/include/Eigen", "-I/opt/Qt/6.9.0/macos/lib/QtCharts.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtOpenGLWidgets.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtWidgets.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtOpenGL.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtGui.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtCore.framework/Headers", "-I/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework/Headers", "-I/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/AGL.framework/Headers", "-I/opt/Qt/6.9.0/macos/mkspecs/macx-clang", "-F", "/opt/Qt/6.9.0/macos/lib", "-F", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks", "-isystem", "/usr/local/include/c++/v1", "-isystem", "/opt/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-isystem", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Desktop/projects/sensuser/repo/sensuser/losscurvewidget.cpp"], "directory": "/Users/<USER>/Desktop/projects/sensuser/repo/sensuser/.qtc_clangd", "file": "/Users/<USER>/Desktop/projects/sensuser/repo/sensuser/losscurvewidget.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-stdlib=libc++", "-std=gnu++1z", "-is<PERSON><PERSON>", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-mmacosx-version-min=12", "-Wall", "-Wextra", "-fsyntax-only", "--target=arm64-apple-darwin24.5.0", "-DQT_NO_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Qt Creator.app/Contents/Resources/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Qt Creator.app/Contents/Resources/cplusplus/wrappedQtHeaders/QtCore", "-I/Users/<USER>/Desktop/projects/sensuser/repo/sensuser", "-I/usr/local/include/Eigen", "-I/opt/Qt/6.9.0/macos/lib/QtCharts.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtOpenGLWidgets.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtWidgets.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtOpenGL.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtGui.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtCore.framework/Headers", "-I/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework/Headers", "-I/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/AGL.framework/Headers", "-I/opt/Qt/6.9.0/macos/mkspecs/macx-clang", "-F", "/opt/Qt/6.9.0/macos/lib", "-F", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks", "-isystem", "/usr/local/include/c++/v1", "-isystem", "/opt/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-isystem", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Desktop/projects/sensuser/repo/sensuser/mainwindow.h"], "directory": "/Users/<USER>/Desktop/projects/sensuser/repo/sensuser/.qtc_clangd", "file": "/Users/<USER>/Desktop/projects/sensuser/repo/sensuser/mainwindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-stdlib=libc++", "-std=gnu++1z", "-is<PERSON><PERSON>", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-mmacosx-version-min=12", "-Wall", "-Wextra", "-fsyntax-only", "--target=arm64-apple-darwin24.5.0", "-DQT_NO_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Qt Creator.app/Contents/Resources/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Qt Creator.app/Contents/Resources/cplusplus/wrappedQtHeaders/QtCore", "-I/Users/<USER>/Desktop/projects/sensuser/repo/sensuser", "-I/usr/local/include/Eigen", "-I/opt/Qt/6.9.0/macos/lib/QtCharts.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtOpenGLWidgets.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtWidgets.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtOpenGL.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtGui.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtCore.framework/Headers", "-I/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework/Headers", "-I/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/AGL.framework/Headers", "-I/opt/Qt/6.9.0/macos/mkspecs/macx-clang", "-F", "/opt/Qt/6.9.0/macos/lib", "-F", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks", "-isystem", "/usr/local/include/c++/v1", "-isystem", "/opt/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-isystem", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Desktop/projects/sensuser/repo/sensuser/mlp.h"], "directory": "/Users/<USER>/Desktop/projects/sensuser/repo/sensuser/.qtc_clangd", "file": "/Users/<USER>/Desktop/projects/sensuser/repo/sensuser/mlp.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-stdlib=libc++", "-std=gnu++1z", "-is<PERSON><PERSON>", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-mmacosx-version-min=12", "-Wall", "-Wextra", "-fsyntax-only", "--target=arm64-apple-darwin24.5.0", "-DQT_NO_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Qt Creator.app/Contents/Resources/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Qt Creator.app/Contents/Resources/cplusplus/wrappedQtHeaders/QtCore", "-I/Users/<USER>/Desktop/projects/sensuser/repo/sensuser", "-I/usr/local/include/Eigen", "-I/opt/Qt/6.9.0/macos/lib/QtCharts.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtOpenGLWidgets.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtWidgets.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtOpenGL.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtGui.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtCore.framework/Headers", "-I/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework/Headers", "-I/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/AGL.framework/Headers", "-I/opt/Qt/6.9.0/macos/mkspecs/macx-clang", "-F", "/opt/Qt/6.9.0/macos/lib", "-F", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks", "-isystem", "/usr/local/include/c++/v1", "-isystem", "/opt/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-isystem", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Desktop/projects/sensuser/repo/sensuser/layer.h"], "directory": "/Users/<USER>/Desktop/projects/sensuser/repo/sensuser/.qtc_clangd", "file": "/Users/<USER>/Desktop/projects/sensuser/repo/sensuser/layer.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-stdlib=libc++", "-std=gnu++1z", "-is<PERSON><PERSON>", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-mmacosx-version-min=12", "-Wall", "-Wextra", "-fsyntax-only", "--target=arm64-apple-darwin24.5.0", "-DQT_NO_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Qt Creator.app/Contents/Resources/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Qt Creator.app/Contents/Resources/cplusplus/wrappedQtHeaders/QtCore", "-I/Users/<USER>/Desktop/projects/sensuser/repo/sensuser", "-I/usr/local/include/Eigen", "-I/opt/Qt/6.9.0/macos/lib/QtCharts.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtOpenGLWidgets.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtWidgets.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtOpenGL.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtGui.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtCore.framework/Headers", "-I/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework/Headers", "-I/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/AGL.framework/Headers", "-I/opt/Qt/6.9.0/macos/mkspecs/macx-clang", "-F", "/opt/Qt/6.9.0/macos/lib", "-F", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks", "-isystem", "/usr/local/include/c++/v1", "-isystem", "/opt/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-isystem", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Desktop/projects/sensuser/repo/sensuser/trainingworker.h"], "directory": "/Users/<USER>/Desktop/projects/sensuser/repo/sensuser/.qtc_clangd", "file": "/Users/<USER>/Desktop/projects/sensuser/repo/sensuser/trainingworker.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-stdlib=libc++", "-std=gnu++1z", "-is<PERSON><PERSON>", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-mmacosx-version-min=12", "-Wall", "-Wextra", "-fsyntax-only", "--target=arm64-apple-darwin24.5.0", "-DQT_NO_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Qt Creator.app/Contents/Resources/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Qt Creator.app/Contents/Resources/cplusplus/wrappedQtHeaders/QtCore", "-I/Users/<USER>/Desktop/projects/sensuser/repo/sensuser", "-I/usr/local/include/Eigen", "-I/opt/Qt/6.9.0/macos/lib/QtCharts.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtOpenGLWidgets.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtWidgets.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtOpenGL.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtGui.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtCore.framework/Headers", "-I/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework/Headers", "-I/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/AGL.framework/Headers", "-I/opt/Qt/6.9.0/macos/mkspecs/macx-clang", "-F", "/opt/Qt/6.9.0/macos/lib", "-F", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks", "-isystem", "/usr/local/include/c++/v1", "-isystem", "/opt/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-isystem", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Desktop/projects/sensuser/repo/sensuser/losscurvewidget.h"], "directory": "/Users/<USER>/Desktop/projects/sensuser/repo/sensuser/.qtc_clangd", "file": "/Users/<USER>/Desktop/projects/sensuser/repo/sensuser/losscurvewidget.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-stdlib=libc++", "-std=gnu++1z", "-is<PERSON><PERSON>", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-mmacosx-version-min=12", "-Wall", "-Wextra", "-fsyntax-only", "--target=arm64-apple-darwin24.5.0", "-DQT_NO_DEBUG", "-DQT_CHARTS_LIB", "-DQT_OPENGLWIDGETS_LIB", "-DQT_WIDGETS_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/opt/Qt/Qt Creator.app/Contents/Resources/cplusplus/wrappedQtHeaders", "-I/opt/Qt/Qt Creator.app/Contents/Resources/cplusplus/wrappedQtHeaders/QtCore", "-I/Users/<USER>/Desktop/projects/sensuser/repo/sensuser", "-I/usr/local/include/Eigen", "-I/opt/Qt/6.9.0/macos/lib/QtCharts.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtOpenGLWidgets.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtWidgets.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtOpenGL.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtGui.framework/Headers", "-I/opt/Qt/6.9.0/macos/lib/QtCore.framework/Headers", "-I/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework/Headers", "-I/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/AGL.framework/Headers", "-I/opt/Qt/6.9.0/macos/mkspecs/macx-clang", "-F", "/opt/Qt/6.9.0/macos/lib", "-F", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks", "-isystem", "/usr/local/include/c++/v1", "-isystem", "/opt/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-isystem", "/Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Desktop/projects/sensuser/repo/sensuser/ui_mainwindow.h"], "directory": "/Users/<USER>/Desktop/projects/sensuser/repo/sensuser/.qtc_clangd", "file": "/Users/<USER>/Desktop/projects/sensuser/repo/sensuser/ui_mainwindow.h"}]